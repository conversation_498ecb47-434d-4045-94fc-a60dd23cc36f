using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Windows.Forms;
using Npgsql;
using ProManage.Modules.Connections;
using ProManage.Modules.Data;
using ProManage.Modules.Helpers;
using ProManage.Modules.Models.UserMasterForm;
using ProManage.Modules.Services;
using ProManage.Modules.UI;

namespace ProManage.Modules.Data.UserMasterForm
{
    /// <summary>
    /// Repository class for UserMasterForm database operations
    /// </summary>
    public static class UserMasterFormRepository
    {
        /// <summary>
        /// Gets all users from the database
        /// </summary>
        /// <returns>List of users</returns>
        public static List<UserMasterFormModel> GetAllUsers()
        {
            try
            {
                ProgressIndicatorService.Instance.ShowProgress("Loading users...");

                var query = SQLQueryLoader.LoadNamedQuery(
                    SQLQueries.User.MODULE_NAME,
                    SQLQueries.User.GET_USER,
                    SQLQueries.User.GetUser.GET_ALL_USERS
                );

                var users = new List<UserMasterFormModel>();

                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();
                    using (var command = new NpgsqlCommand(query, connection))
                    {
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var user = new UserMasterFormModel
                                {
                                    UserId = GetIntValue(reader, "user_id", "id") ?? 0,
                                    Username = GetStringValue(reader, "username"),
                                    FullName = GetStringValue(reader, "full_name"),
                                    Email = GetStringValue(reader, "email"),
                                    Role = GetStringValue(reader, "role") ?? "User",
                                    Department = GetStringValue(reader, "department"),
                                    Phone = GetStringValue(reader, "phone"),
                                    Designation = GetStringValue(reader, "designation"),
                                    ShortName = GetStringValue(reader, "short_name"),
                                    IsActive = GetBooleanValue(reader, "is_active"),
                                    LastLoginDate = GetDateTimeValue(reader, "last_login_date"),
                                    CreatedDate = GetDateTimeValue(reader, "created_date")
                                };
                                users.Add(user);
                            }
                        }
                    }
                }

                Debug.WriteLine($"Loaded {users.Count} users from database");
                return users;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error loading users: {ex.Message}");
                MessageBox.Show($"Error loading users: {ex.Message}", "Database Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                return new List<UserMasterFormModel>();
            }
            finally
            {
                ProgressIndicatorService.Instance.HideProgress();
            }
        }

        /// <summary>
        /// Gets a user by ID from the database
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <returns>User model or null if not found</returns>
        public static UserMasterFormModel GetUserById(int userId)
        {
            try
            {
                ProgressIndicatorService.Instance.ShowProgress("Loading user...");

                var query = SQLQueryLoader.LoadNamedQuery(
                    SQLQueries.User.MODULE_NAME,
                    SQLQueries.User.GET_USER,
                    SQLQueries.User.GetUser.GET_USER_BY_ID
                );

                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();
                    using (var command = new NpgsqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@user_id", userId);

                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                return new UserMasterFormModel
                                {
                                    UserId = GetIntValue(reader, "user_id", "id") ?? 0,
                                    Username = GetStringValue(reader, "username"),
                                    FullName = GetStringValue(reader, "full_name"),
                                    Email = GetStringValue(reader, "email"),
                                    Role = GetStringValue(reader, "role") ?? "User",
                                    Department = GetStringValue(reader, "department"),
                                    Phone = GetStringValue(reader, "phone"),
                                    Designation = GetStringValue(reader, "designation"),
                                    ShortName = GetStringValue(reader, "short_name"),
                                    PhotoPath = GetStringValue(reader, "photo_path"),
                                    PasswordHash = GetStringValue(reader, "password_hash"),
                                    PasswordSalt = GetStringValue(reader, "password_salt"),
                                    IsActive = GetBooleanValue(reader, "is_active"),
                                    LastLoginDate = GetDateTimeValue(reader, "last_login_date"),
                                    CreatedDate = GetDateTimeValue(reader, "created_date")
                                };
                            }
                        }
                    }
                }

                Debug.WriteLine($"User with ID {userId} not found");
                return null;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error loading user by ID {userId}: {ex.Message}");
                MessageBox.Show($"Error loading user: {ex.Message}", "Database Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                return null;
            }
            finally
            {
                ProgressIndicatorService.Instance.HideProgress();
            }
        }

        /// <summary>
        /// Saves a user to the database (insert or update)
        /// </summary>
        /// <param name="user">User model to save</param>
        /// <returns>True if successful, false otherwise</returns>
        public static bool SaveUser(UserMasterFormModel user)
        {
            try
            {
                ProgressIndicatorService.Instance.ShowProgress("Saving user...");

                if (user.UserId == 0)
                {
                    // Insert new user
                    return InsertUser(user);
                }
                else
                {
                    // Update existing user
                    return UpdateUser(user);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error saving user: {ex.Message}");
                MessageBox.Show($"Error saving user: {ex.Message}", "Database Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
            finally
            {
                ProgressIndicatorService.Instance.HideProgress();
            }
        }

        /// <summary>
        /// Inserts a new user into the database
        /// </summary>
        /// <param name="user">User model to insert</param>
        /// <returns>True if successful, false otherwise</returns>
        private static bool InsertUser(UserMasterFormModel user)
        {
            try
            {
                var query = SQLQueryLoader.LoadNamedQuery(
                    SQLQueries.User.MODULE_NAME,
                    SQLQueries.User.SAVE_USER,
                    SQLQueries.User.SaveUser.INSERT_USER
                );

                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();
                    using (var command = new NpgsqlCommand(query, connection))
                    {
                        // Add parameters
                        command.Parameters.AddWithValue("@username", user.Username ?? string.Empty);
                        command.Parameters.AddWithValue("@password_hash", user.PasswordHash ?? string.Empty);
                        command.Parameters.AddWithValue("@password_salt", user.PasswordSalt ?? string.Empty);
                        command.Parameters.AddWithValue("@full_name", user.FullName ?? string.Empty);
                        command.Parameters.AddWithValue("@email", user.Email ?? string.Empty);
                        command.Parameters.AddWithValue("@role", user.Role ?? "User");
                        command.Parameters.AddWithValue("@department", user.Department ?? string.Empty);
                        command.Parameters.AddWithValue("@phone", user.Phone ?? string.Empty);
                        command.Parameters.AddWithValue("@designation", user.Designation ?? string.Empty);
                        command.Parameters.AddWithValue("@short_name", user.ShortName ?? string.Empty);
                        command.Parameters.AddWithValue("@photo_path", user.PhotoPath ?? string.Empty);
                        command.Parameters.AddWithValue("@edit_password", string.Empty); // Will be set separately if needed
                        command.Parameters.AddWithValue("@is_active", user.IsActive);

                        // Execute and get the new user ID
                        var result = command.ExecuteScalar();
                        if (result != null && int.TryParse(result.ToString(), out int newUserId))
                        {
                            user.UserId = newUserId;
                            Debug.WriteLine($"User inserted successfully with ID: {newUserId}");
                            return true;
                        }
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error inserting user: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Updates an existing user in the database
        /// </summary>
        /// <param name="user">User model to update</param>
        /// <returns>True if successful, false otherwise</returns>
        private static bool UpdateUser(UserMasterFormModel user)
        {
            try
            {
                var query = SQLQueryLoader.LoadNamedQuery(
                    SQLQueries.User.MODULE_NAME,
                    SQLQueries.User.SAVE_USER,
                    SQLQueries.User.SaveUser.UPDATE_USER
                );

                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();
                    using (var command = new NpgsqlCommand(query, connection))
                    {
                        // Add parameters
                        command.Parameters.AddWithValue("@user_id", user.UserId);
                        command.Parameters.AddWithValue("@username", user.Username ?? string.Empty);
                        command.Parameters.AddWithValue("@full_name", user.FullName ?? string.Empty);
                        command.Parameters.AddWithValue("@email", user.Email ?? string.Empty);
                        command.Parameters.AddWithValue("@role", user.Role ?? "User");
                        command.Parameters.AddWithValue("@department", user.Department ?? string.Empty);
                        command.Parameters.AddWithValue("@phone", user.Phone ?? string.Empty);
                        command.Parameters.AddWithValue("@designation", user.Designation ?? string.Empty);
                        command.Parameters.AddWithValue("@short_name", user.ShortName ?? string.Empty);
                        command.Parameters.AddWithValue("@photo_path", user.PhotoPath ?? string.Empty);
                        command.Parameters.AddWithValue("@is_active", user.IsActive);

                        int rowsAffected = command.ExecuteNonQuery();
                        Debug.WriteLine($"User updated successfully. Rows affected: {rowsAffected}");
                        return rowsAffected > 0;
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error updating user: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Updates user password (login password with hash + salt)
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="passwordHash">Password hash</param>
        /// <param name="passwordSalt">Password salt</param>
        /// <returns>True if successful, false otherwise</returns>
        public static bool UpdateUserPassword(int userId, string passwordHash, string passwordSalt)
        {
            try
            {
                ProgressIndicatorService.Instance.ShowProgress("Updating password...");

                var query = SQLQueryLoader.LoadNamedQuery(
                    SQLQueries.User.MODULE_NAME,
                    SQLQueries.User.SAVE_USER,
                    SQLQueries.User.SaveUser.UPDATE_USER_PASSWORD
                );

                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();
                    using (var command = new NpgsqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@user_id", userId);
                        command.Parameters.AddWithValue("@password_hash", passwordHash);
                        command.Parameters.AddWithValue("@password_salt", passwordSalt);

                        int rowsAffected = command.ExecuteNonQuery();
                        Debug.WriteLine($"User password updated. Rows affected: {rowsAffected}");
                        return rowsAffected > 0;
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error updating user password: {ex.Message}");
                MessageBox.Show($"Error updating password: {ex.Message}", "Database Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
            finally
            {
                ProgressIndicatorService.Instance.HideProgress();
            }
        }

        /// <summary>
        /// Updates user edit password (hash only)
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="editPasswordHash">Edit password hash</param>
        /// <returns>True if successful, false otherwise</returns>
        public static bool UpdateEditPassword(int userId, string editPasswordHash)
        {
            try
            {
                ProgressIndicatorService.Instance.ShowProgress("Updating edit password...");

                var query = SQLQueryLoader.LoadNamedQuery(
                    SQLQueries.User.MODULE_NAME,
                    SQLQueries.User.SAVE_USER,
                    SQLQueries.User.SaveUser.UPDATE_EDIT_PASSWORD
                );

                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();
                    using (var command = new NpgsqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@user_id", userId);
                        command.Parameters.AddWithValue("@edit_password", editPasswordHash);

                        int rowsAffected = command.ExecuteNonQuery();
                        Debug.WriteLine($"User edit password updated. Rows affected: {rowsAffected}");
                        return rowsAffected > 0;
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error updating edit password: {ex.Message}");
                MessageBox.Show($"Error updating edit password: {ex.Message}", "Database Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
            finally
            {
                ProgressIndicatorService.Instance.HideProgress();
            }
        }

        /// <summary>
        /// Deletes a user (soft delete by setting is_active to false)
        /// </summary>
        /// <param name="userId">User ID to delete</param>
        /// <returns>True if successful, false otherwise</returns>
        public static bool DeleteUser(int userId)
        {
            try
            {
                ProgressIndicatorService.Instance.ShowProgress("Deleting user...");

                var query = SQLQueryLoader.LoadNamedQuery(
                    SQLQueries.User.MODULE_NAME,
                    SQLQueries.User.SAVE_USER,
                    SQLQueries.User.SaveUser.DELETE_USER
                );

                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();
                    using (var command = new NpgsqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@user_id", userId);

                        int rowsAffected = command.ExecuteNonQuery();
                        Debug.WriteLine($"User deleted (soft delete). Rows affected: {rowsAffected}");
                        return rowsAffected > 0;
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error deleting user: {ex.Message}");
                MessageBox.Show($"Error deleting user: {ex.Message}", "Database Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
            finally
            {
                ProgressIndicatorService.Instance.HideProgress();
            }
        }

        #region Helper Methods

        /// <summary>
        /// Helper method to check if a column exists in the reader
        /// </summary>
        private static bool HasColumn(NpgsqlDataReader reader, string columnName)
        {
            try
            {
                return reader.GetOrdinal(columnName) >= 0;
            }
            catch (IndexOutOfRangeException)
            {
                return false;
            }
        }

        /// <summary>
        /// Helper method to get string value from reader with fallback column names
        /// </summary>
        private static string GetStringValue(NpgsqlDataReader reader, params string[] columnNames)
        {
            foreach (var columnName in columnNames)
            {
                if (HasColumn(reader, columnName))
                {
                    return reader[columnName] == DBNull.Value ? null : reader[columnName].ToString();
                }
            }
            return null;
        }

        /// <summary>
        /// Helper method to get DateTime value from reader with fallback column names
        /// </summary>
        private static DateTime? GetDateTimeValue(NpgsqlDataReader reader, params string[] columnNames)
        {
            foreach (var columnName in columnNames)
            {
                if (HasColumn(reader, columnName))
                {
                    return reader[columnName] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(reader[columnName]);
                }
            }
            return null;
        }

        /// <summary>
        /// Helper method to get boolean value from reader
        /// </summary>
        private static bool GetBooleanValue(NpgsqlDataReader reader, string columnName)
        {
            if (HasColumn(reader, columnName))
            {
                return reader[columnName] != DBNull.Value && Convert.ToBoolean(reader[columnName]);
            }
            return false;
        }

        /// <summary>
        /// Helper method to get int value from reader with fallback column names
        /// </summary>
        private static int? GetIntValue(NpgsqlDataReader reader, params string[] columnNames)
        {
            foreach (var columnName in columnNames)
            {
                if (HasColumn(reader, columnName))
                {
                    return reader[columnName] == DBNull.Value ? (int?)null : Convert.ToInt32(reader[columnName]);
                }
            }
            return null;
        }

        #endregion
    }
}
